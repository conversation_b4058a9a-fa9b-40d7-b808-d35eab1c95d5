#!/bin/bash

# ============================================================================
# PostgreSQL 17.5 + Patroni + etcd 一键安装脚本 (Debian版)
# 适用于: Debian 11+ / Ubuntu 20.04+
# 作者: linshiqiang
# 日期: 2025-01-02
# ============================================================================

# ============================================================================
# 配置参数区域 - 根据实际环境修改
# ============================================================================

# 网络配置 - 根据实际环境修改IP地址
export PG_NODE1_IP="**********"                 # PostgreSQL节点1 IP
export PG_NODE2_IP="**********"                 # PostgreSQL节点2 IP
export PG_NODE3_IP="***********"                 # PostgreSQL节点3 IP
export HAPROXY_NODE1_IP="**********"            # HAProxy节点1 IP
export HAPROXY_NODE2_IP="**********"            # HAProxy节点2 IP
export HAPROXY_VIP="**********0"                # HAProxy虚拟IP
export NETWORK_SEGMENT="**********/24"           # 网络段

# 基础配置
export INSTALL_USER="postgres"                    # PostgreSQL运行用户
export INSTALL_GROUP="postgres"                   # PostgreSQL运行用户组
export BASE_DIR="/home"                           # 基础安装目录

# PostgreSQL配置
export PG_VERSION="17.5"                          # PostgreSQL版本
export PG_PORT="5432"                             # PostgreSQL端口
export PG_DATA_DIR="${BASE_DIR}/postgresql/data"  # 数据目录
export PG_LOG_DIR="${BASE_DIR}/postgresql/logs"   # 日志目录
export PG_ARCHIVE_DIR="${BASE_DIR}/postgresql/archive"  # 归档目录
export PG_BACKUP_DIR="${BASE_DIR}/postgresql/backup"    # 备份目录
export PG_SCRIPTS_DIR="${BASE_DIR}/postgresql/scripts"  # 脚本目录
export PG_SRC_DIR="${BASE_DIR}/postgresql/src"    # 源码目录

# 数据库用户密码配置
export POSTGRES_PASSWORD="PostgresPass123!"       # postgres超级用户密码
export REPLICATOR_PASSWORD="ReplicatorPass123!"   # 复制用户密码
export REWIND_PASSWORD="RewindPass123!"           # rewind用户密码

# 集群配置
export CLUSTER_NAME="postgres-cluster"            # 集群名称
export NODE_NAME=""                                # 节点名称(自动检测)
export NODE_IP=""                                  # 节点IP(自动检测)

# etcd配置
export ETCD_VERSION="v3.5.10"                     # etcd版本
export ETCD_PORT_CLIENT="2379"                    # etcd客户端端口
export ETCD_PORT_PEER="2380"                      # etcd节点间通信端口
export ETCD_DATA_DIR="${BASE_DIR}/etcd/data"      # etcd数据目录
export ETCD_LOG_DIR="${BASE_DIR}/etcd/logs"       # etcd日志目录

# Patroni配置
export PATRONI_PORT="8008"                        # Patroni REST API端口
export PATRONI_CONFIG_DIR="${BASE_DIR}/patroni/config"  # Patroni配置目录
export PATRONI_LOG_DIR="${BASE_DIR}/patroni/logs"       # Patroni日志目录

# 集群节点配置 (基于上面的IP配置自动生成)
export ETCD_CLUSTER_NODES=(
    "etcd-1=http://${PG_NODE1_IP}:${ETCD_PORT_PEER}"
    "etcd-2=http://${PG_NODE2_IP}:${ETCD_PORT_PEER}"
    "etcd-3=http://${PG_NODE3_IP}:${ETCD_PORT_PEER}"
)

export ETCD_ENDPOINTS=(
    "${PG_NODE1_IP}:${ETCD_PORT_CLIENT}"
    "${PG_NODE2_IP}:${ETCD_PORT_CLIENT}"
    "${PG_NODE3_IP}:${ETCD_PORT_CLIENT}"
)

# 网络配置映射
export NODE_MAPPING=(
    "${PG_NODE1_IP}:pg-node1:etcd-1"
    "${PG_NODE2_IP}:pg-node2:etcd-2"
    "${PG_NODE3_IP}:pg-node3:etcd-3"
)

# ============================================================================
# 函数定义区域
# ============================================================================

# 日志函数
log_info() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] $1"
}

log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $1" >&2
}

log_warn() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [WARN] $1"
}

# 检查命令执行结果
check_result() {
    if [ $? -eq 0 ]; then
        log_info "$1 成功"
    else
        log_error "$1 失败"
        exit 1
    fi
}

# 检测当前节点信息
detect_node_info() {
    log_info "检测当前节点信息..."

    # 获取本机IP地址
    NODE_IP=$(ip route get ******* | awk '{print $7; exit}')

    # 根据IP确定节点名称和etcd名称
    for mapping in "${NODE_MAPPING[@]}"; do
        IFS=':' read -r ip node_name etcd_name <<< "$mapping"
        if [ "$ip" = "$NODE_IP" ]; then
            NODE_NAME="$node_name"
            ETCD_NAME="$etcd_name"
            break
        fi
    done

    if [ -z "$NODE_NAME" ]; then
        log_error "无法确定节点名称，请检查NODE_MAPPING配置"
        exit 1
    fi

    log_info "当前节点: $NODE_NAME, IP: $NODE_IP, etcd名称: $ETCD_NAME"
}

# 检查系统环境
check_system() {
    log_info "检查系统环境..."

    # 检查操作系统
    if [ ! -f /etc/debian_version ]; then
        log_error "此脚本仅支持 Debian/Ubuntu 系统"
        exit 1
    fi

    # 检查系统版本
    if command -v lsb_release >/dev/null 2>&1; then
        OS_VERSION=$(lsb_release -rs | cut -d. -f1)
        OS_NAME=$(lsb_release -is)

        if [ "$OS_NAME" = "Debian" ] && [ "$OS_VERSION" -lt 11 ]; then
            log_error "Debian版本过低，需要 Debian 11+"
            exit 1
        elif [ "$OS_NAME" = "Ubuntu" ] && [ "$OS_VERSION" -lt 20 ]; then
            log_error "Ubuntu版本过低，需要 Ubuntu 20.04+"
            exit 1
        fi
    fi

    # 检查是否为root用户
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用root用户执行此脚本"
        exit 1
    fi

    log_info "系统环境检查通过: $(lsb_release -ds 2>/dev/null || cat /etc/debian_version)"
}

# 安装系统依赖
install_dependencies() {
    log_info "安装系统依赖包..."

    # 更新软件包列表
    apt update
    check_result "软件包列表更新"

    # 安装编译依赖
    apt install -y build-essential
    check_result "编译工具安装"

    # 安装PostgreSQL编译依赖
    apt install -y \
        passwd \
        gcc g++ make cmake \
        libreadline-dev zlib1g-dev libssl-dev \
        libxml2-dev libxslt1-dev \
        python3 python3-pip python3-dev \
        wget curl vim git htop iotop \
        net-tools telnet netcat-openbsd \
        chrony pkg-config \
        libedit-dev libicu-dev \
        uuid-dev libossp-uuid-dev \
        bison flex gettext \
        libkrb5-dev libpam0g-dev \
        libldap2-dev libsystemd-dev
    check_result "依赖包安装"

    # 配置时间同步
    systemctl enable chrony
    systemctl start chrony
    check_result "时间同步配置"
}

# 创建用户和目录
create_user_and_dirs() {
    log_info "创建用户和目录结构..."

#    # 创建postgres用户
   if ! id "$INSTALL_USER" &>/dev/null; then
       /usr/sbin/useradd -r -s /bin/bash "$INSTALL_USER"
       check_result "创建postgres用户"
   else
       log_info "用户 $INSTALL_USER 已存在"
   fi

    # 创建目录结构
    mkdir -p "$PG_DATA_DIR" "$PG_LOG_DIR" "$PG_ARCHIVE_DIR" "$PG_BACKUP_DIR" "$PG_SCRIPTS_DIR" "$PG_SRC_DIR"
    mkdir -p "$ETCD_DATA_DIR" "$ETCD_LOG_DIR"
    mkdir -p "$PATRONI_CONFIG_DIR" "$PATRONI_LOG_DIR"

    # 设置目录权限
    chown -R "$INSTALL_USER:$INSTALL_GROUP" "${BASE_DIR}/postgresql"
    chown -R "$INSTALL_USER:$INSTALL_GROUP" "${BASE_DIR}/etcd"
    chown -R "$INSTALL_USER:$INSTALL_GROUP" "${BASE_DIR}/patroni"

    chmod 700 "$PG_DATA_DIR"
    chmod 755 "$PG_LOG_DIR" "$PG_ARCHIVE_DIR" "$PG_BACKUP_DIR" "$PG_SCRIPTS_DIR"

    check_result "目录创建和权限设置"
}

# 编译安装PostgreSQL 17.5
install_postgresql() {
    log_info "开始编译安装PostgreSQL $PG_VERSION..."

    cd "$PG_SRC_DIR"

    # 下载PostgreSQL源码
    if [ ! -f "postgresql-${PG_VERSION}.tar.gz" ]; then
        log_info "下载PostgreSQL源码..."
#        wget "https://ftp.postgresql.org/pub/source/v${PG_VERSION}/postgresql-${PG_VERSION}.tar.gz"
        wget http://*************:9999/common/postgresql/postgresql-${PG_VERSION}.tar.gz
        check_result "PostgreSQL源码下载"
    fi

    # 解压源码
    if [ ! -d "postgresql-${PG_VERSION}" ]; then
        tar -xzf "postgresql-${PG_VERSION}.tar.gz"
        check_result "PostgreSQL源码解压"
    fi

    cd "postgresql-${PG_VERSION}"

    # 配置编译选项 (Debian/Ubuntu特定)
    log_info "配置PostgreSQL编译选项..."
    ./configure \
        --prefix=/usr/local/pgsql \
        --with-openssl \
        --with-libxml \
        --with-libxslt \
        --enable-thread-safety \
        --with-readline \
        --with-zlib \
        --with-uuid=ossp \
        --with-icu \
        --enable-debug
    check_result "PostgreSQL配置"

    # 编译
    log_info "编译PostgreSQL (这可能需要几分钟)..."
    make -j$(nproc)
    check_result "PostgreSQL编译"

    # 安装
    log_info "安装PostgreSQL..."
    make install
    check_result "PostgreSQL安装"

    # 编译安装contrib模块
    log_info "编译安装contrib模块..."
    cd contrib
    make -j$(nproc)
    make install
    check_result "contrib模块安装"

    # 创建软链接
    ln -sf /usr/local/pgsql/bin/* /usr/local/bin/

    # 配置环境变量
    cat > /etc/profile.d/postgresql.sh << 'EOF'
export PGHOME=/usr/local/pgsql
export PATH=$PGHOME/bin:$PATH
export LD_LIBRARY_PATH=$PGHOME/lib:$LD_LIBRARY_PATH
export MANPATH=$PGHOME/share/man:$MANPATH
EOF

    # 配置postgres用户环境变量
    cat >> "/home/<USER>/.bashrc" << EOF
export PGDATA=$PG_DATA_DIR
export PGPORT=$PG_PORT
export PGUSER=$INSTALL_USER
export PGHOME=/usr/local/pgsql
export PATH=\$PGHOME/bin:\$PATH
export LD_LIBRARY_PATH=\$PGHOME/lib:\$LD_LIBRARY_PATH
export MANPATH=\$PGHOME/share/man:\$MANPATH
EOF

    # 更新动态链接库缓存
    echo "/usr/local/pgsql/lib" > /etc/ld.so.conf.d/postgresql.conf
    ldconfig

    log_info "PostgreSQL $PG_VERSION 安装完成"
}

# 安装etcd
install_etcd() {
    log_info "安装etcd $ETCD_VERSION..."

    cd /tmp

    # 下载etcd
    if [ ! -f "etcd-${ETCD_VERSION}-linux-amd64.tar.gz" ]; then
        log_info "下载etcd..."
#        wget "https://github.com/etcd-io/etcd/releases/download/${ETCD_VERSION}/etcd-${ETCD_VERSION}-linux-amd64.tar.gz"
        wget http://*************:9999/common/etcd/etcd-${ETCD_VERSION}-linux-amd64.tar.gz
        check_result "etcd下载"
    fi

    # 解压并安装
    tar -xzf "etcd-${ETCD_VERSION}-linux-amd64.tar.gz"
    cp "etcd-${ETCD_VERSION}-linux-amd64/etcd"* /usr/local/bin/
    chmod +x /usr/local/bin/etcd*

    # 清理临时文件
    rm -rf "etcd-${ETCD_VERSION}-linux-amd64"*

    check_result "etcd安装"
}

# 配置Python3清华源
configure_python_mirror() {
    log_info "配置Python3清华源..."

    # 创建pip配置目录
    mkdir -p ~/.pip
    mkdir -p /home/<USER>/.pip

    # 配置pip清华源
    cat > ~/.pip/pip.conf << 'EOF'
[global]
index-url = https://pypi.tuna.tsinghua.edu.cn/simple
trusted-host = pypi.tuna.tsinghua.edu.cn
timeout = 120
EOF

    # 为postgres用户配置pip清华源
    cat > /home/<USER>/.pip/pip.conf << 'EOF'
[global]
index-url = https://pypi.tuna.tsinghua.edu.cn/simple
trusted-host = pypi.tuna.tsinghua.edu.cn
timeout = 120
EOF

    # 设置权限
    chown -R $INSTALL_USER:$INSTALL_GROUP /home/<USER>/.pip

    check_result "Python3清华源配置"
}

# 安装Python依赖和Patroni
install_patroni() {
    log_info "安装Patroni..."

    # 安装必要的Python包
    apt install -y python3-full python3-dev

    # 移除Python外部管理限制 (仅用于服务器部署)
    if [ -f /usr/lib/python*/EXTERNALLY-MANAGED ]; then
        rm -f /usr/lib/python*/EXTERNALLY-MANAGED
        log_info "移除Python外部管理限制"
    fi

    # 配置Python清华源
    configure_python_mirror

    # 升级pip (使用清华源)
    python3 -m pip install --upgrade pip --break-system-packages -i https://pypi.tuna.tsinghua.edu.cn/simple
    check_result "pip升级"

    # 安装Patroni及依赖 (使用清华源)
    pip3 install patroni[etcd]==3.2.0 --break-system-packages -i https://pypi.tuna.tsinghua.edu.cn/simple
    pip3 install psycopg2-binary==2.9.9 --break-system-packages -i https://pypi.tuna.tsinghua.edu.cn/simple
    pip3 install python-etcd==0.4.5 --break-system-packages -i https://pypi.tuna.tsinghua.edu.cn/simple
    pip3 install cdiff
    check_result "Patroni安装"

    # 创建Patroni软链接
    ln -sf /usr/local/bin/patroni* /usr/bin/
}

# 配置系统参数
configure_system() {
    log_info "配置系统参数..."

    # 配置内核参数
    cat >> /etc/sysctl.conf << EOF

# PostgreSQL优化参数
kernel.shmmax = 68719476736
kernel.shmall = 4294967296
kernel.shmmni = 4096
kernel.sem = 50100 64128000 50100 1280
fs.file-max = 7672460
net.ipv4.ip_local_port_range = 9000 65000
net.core.rmem_default = 262144
net.core.rmem_max = 4194304
net.core.wmem_default = 262144
net.core.wmem_max = 1048576
vm.swappiness = 1
vm.dirty_background_ratio = 3
vm.dirty_ratio = 10
EOF

    /sbin/sysctl -p
    check_result "内核参数配置"

    # 配置用户资源限制
    cat >> /etc/security/limits.conf << EOF

# PostgreSQL用户资源限制
$INSTALL_USER soft nofile 65536
$INSTALL_USER hard nofile 65536
$INSTALL_USER soft nproc 32768
$INSTALL_USER hard nproc 32768
$INSTALL_USER soft memlock unlimited
$INSTALL_USER hard memlock unlimited
EOF

    check_result "用户资源限制配置"

    # 关闭防火墙 (如果使用ufw)
    if command -v ufw >/dev/null 2>&1; then
        ufw --force disable 2>/dev/null || true
    fi

    # 禁用AppArmor对PostgreSQL的限制 (如果存在)
    if [ -f /etc/apparmor.d/usr.bin.postgres ]; then
        ln -sf /etc/apparmor.d/usr.bin.postgres /etc/apparmor.d/disable/
        apparmor_parser -R /etc/apparmor.d/usr.bin.postgres 2>/dev/null || true
    fi

    log_info "系统参数配置完成"
}

# 配置etcd
configure_etcd() {
    log_info "配置etcd..."

    # 构建etcd集群字符串
    ETCD_INITIAL_CLUSTER=""
    for node in "${ETCD_CLUSTER_NODES[@]}"; do
        if [ -z "$ETCD_INITIAL_CLUSTER" ]; then
            ETCD_INITIAL_CLUSTER="$node"
        else
            ETCD_INITIAL_CLUSTER="$ETCD_INITIAL_CLUSTER,$node"
        fi
    done

    # 创建etcd配置文件
    cat > "/etc/etcd.conf" << EOF
# etcd配置文件
name: $ETCD_NAME
data-dir: $ETCD_DATA_DIR
listen-client-urls: http://$NODE_IP:$ETCD_PORT_CLIENT,http://127.0.0.1:2379
advertise-client-urls: http://$NODE_IP:$ETCD_PORT_CLIENT
listen-peer-urls: http://$NODE_IP:$ETCD_PORT_PEER
initial-advertise-peer-urls: http://$NODE_IP:$ETCD_PORT_PEER
initial-cluster: $ETCD_INITIAL_CLUSTER
initial-cluster-token: postgres-etcd-cluster
initial-cluster-state: new
log-level: info
log-outputs: [$ETCD_LOG_DIR/etcd.log]
heartbeat-interval: 100
election-timeout: 1000
max-snapshots: 5
max-wals: 5
enable-v2: true

EOF

    check_result "etcd配置"
}

# 初始化PostgreSQL数据库
init_postgresql() {
    log_info "初始化PostgreSQL数据库..."

    # 检查数据目录是否已经初始化
    if [ -f "$PG_DATA_DIR/PG_VERSION" ]; then
        log_info "数据库已经初始化，跳过initdb步骤"
        return 0
    fi

    # 确保数据目录为空
    if [ -d "$PG_DATA_DIR" ] && [ "$(ls -A $PG_DATA_DIR)" ]; then
        log_warn "数据目录不为空，清空后重新初始化"
        rm -rf "$PG_DATA_DIR"/*
    fi

    # 切换到postgres用户初始化数据库
    su - "$INSTALL_USER" -c "
        /usr/local/pgsql/bin/initdb -D $PG_DATA_DIR \
            --encoding=UTF8 \
            --locale=C \
            --data-checksums \
            --auth-local=trust \
            --auth-host=md5
    "


    check_result "PostgreSQL数据库初始化"

    # 创建基础配置文件
    cat > "$PG_DATA_DIR/postgresql.conf" << EOF
# PostgreSQL 17.5 配置文件

# 连接和认证
listen_addresses = '*'
port = $PG_PORT
max_connections = 200
superuser_reserved_connections = 3

# 内存配置
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

# WAL配置
wal_level = replica
wal_buffers = 16MB
min_wal_size = 1GB
max_wal_size = 4GB
wal_log_hints = on
archive_mode = on
archive_command = 'test ! -f $PG_ARCHIVE_DIR/%f && cp %p $PG_ARCHIVE_DIR/%f'

# 复制配置
max_wal_senders = 10
max_replication_slots = 10
hot_standby = on

# 检查点配置
checkpoint_completion_target = 0.9
checkpoint_timeout = 5min

# 日志配置
logging_collector = on
log_destination = 'csvlog'
log_directory = '$PG_LOG_DIR'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_rotation_age = 1d
log_rotation_size = 100MB
log_min_duration_statement = 1000
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on
log_temp_files = 0

# 性能优化
random_page_cost = 1.1
effective_io_concurrency = 200
max_worker_processes = 8
max_parallel_workers_per_gather = 4
max_parallel_workers = 8
max_parallel_maintenance_workers = 4
default_statistics_target = 100

# 其他配置
unix_socket_directories = '/tmp'
shared_preload_libraries = 'pg_stat_statements'
EOF

    # 创建pg_hba.conf
    cat > "$PG_DATA_DIR/pg_hba.conf" << EOF
# PostgreSQL Client Authentication Configuration File

# TYPE  DATABASE        USER            ADDRESS                 METHOD

# "local" is for Unix domain socket connections only
local   all             all                                     trust

# IPv4 local connections:
host    all             all             127.0.0.1/32            md5
host    all             all             $NETWORK_SEGMENT        md5

# IPv6 local connections:
host    all             all             ::1/128                 md5

# Replication connections
host    replication     replicator      127.0.0.1/32            md5
host    replication     replicator      $NETWORK_SEGMENT        md5
EOF

    chown -R "$INSTALL_USER:$INSTALL_GROUP" "$PG_DATA_DIR"
    check_result "PostgreSQL配置文件创建"
}

# 配置Patroni
configure_patroni() {
    log_info "配置Patroni..."

    # 构建etcd endpoints字符串
    ETCD_ENDPOINTS_STR=""
    for endpoint in "${ETCD_ENDPOINTS[@]}"; do
        if [ -z "$ETCD_ENDPOINTS_STR" ]; then
            ETCD_ENDPOINTS_STR="$endpoint"
        else
            ETCD_ENDPOINTS_STR="$ETCD_ENDPOINTS_STR,$endpoint"
        fi
    done

    # 创建Patroni配置文件
    cat > "$PATRONI_CONFIG_DIR/patroni.yml" << EOF
# Patroni配置文件
scope: $CLUSTER_NAME
namespace: /db/
name: $NODE_NAME

restapi:
  listen: $NODE_IP:$PATRONI_PORT
  connect_address: $NODE_IP:$PATRONI_PORT

etcd:
  hosts: $ETCD_ENDPOINTS_STR

bootstrap:
  dcs:
    ttl: 60
    loop_wait: 10
    retry_timeout: 20
    maximum_lag_on_failover: 1048576
    master_start_timeout: 300
    synchronous_mode: false

    postgresql:
      use_pg_rewind: true
      use_slots: true

      parameters:
        max_connections: 200
        shared_buffers: 256MB
        effective_cache_size: 1GB
        work_mem: 4MB
        maintenance_work_mem: 64MB
        wal_level: replica
        wal_buffers: 16MB
        min_wal_size: 1GB
        max_wal_size: 4GB
        wal_log_hints: on
        max_wal_senders: 10
        max_replication_slots: 10
        hot_standby: on
        checkpoint_completion_target: 0.9
        archive_mode: on
        archive_command: 'test ! -f $PG_ARCHIVE_DIR/%f && cp %p $PG_ARCHIVE_DIR/%f'
        logging_collector: on
        log_destination: 'csvlog'
        log_directory: '$PG_LOG_DIR'
        log_filename: 'postgresql-%Y-%m-%d_%H%M%S.log'
        log_rotation_age: 1d
        log_rotation_size: 100MB
        log_min_duration_statement: 1000
        log_checkpoints: on
        log_connections: on
        log_disconnections: on
        log_lock_waits: on
        log_temp_files: 0
        random_page_cost: 1.1
        effective_io_concurrency: 200
        max_worker_processes: 8
        max_parallel_workers_per_gather: 4
        max_parallel_workers: 8
        max_parallel_maintenance_workers: 4
        default_statistics_target: 100
        unix_socket_directories: '/tmp'
        shared_preload_libraries: 'pg_stat_statements'

  initdb:
  - encoding: UTF8
  - data-checksums
  - locale: C

  pg_hba:
  - host replication replicator 127.0.0.1/32 md5
  - host replication replicator $NETWORK_SEGMENT md5
  - host all all 0.0.0.0/0 md5

  users:
    admin:
      password: '$POSTGRES_PASSWORD'
      options:
        - createrole
        - createdb

postgresql:
  listen: 0.0.0.0:$PG_PORT
  connect_address: $NODE_IP:$PG_PORT
  data_dir: $PG_DATA_DIR
  bin_dir: /usr/local/pgsql/bin
  pgpass: /tmp/pgpass_$NODE_NAME

  authentication:
    replication:
      username: replicator
      password: '$REPLICATOR_PASSWORD'
    superuser:
      username: postgres
      password: '$POSTGRES_PASSWORD'
    rewind:
      username: rewind_user
      password: '$REWIND_PASSWORD'

  parameters:
    unix_socket_directories: '/tmp'

tags:
    nofailover: false
    noloadbalance: false
    clonefrom: false
    nosync: false
EOF

    chown -R "$INSTALL_USER:$INSTALL_GROUP" "$PATRONI_CONFIG_DIR"
    check_result "Patroni配置"
}

# 创建systemd服务文件
create_systemd_services() {
    log_info "创建systemd服务文件..."

    # 创建Patroni服务文件
    cat > /etc/systemd/system/patroni.service << EOF
[Unit]
Description=Runners to orchestrate a high-availability PostgreSQL
Documentation=https://patroni.readthedocs.io/
After=syslog.target network.target etcd.service
Wants=network-online.target

[Service]
Type=simple
User=$INSTALL_USER
Group=$INSTALL_GROUP
Environment=PATRONI_CONFIG_FILE=$PATRONI_CONFIG_DIR/patroni.yml
ExecStart=/usr/local/bin/patroni $PATRONI_CONFIG_DIR/patroni.yml
ExecReload=/bin/kill -HUP \$MAINPID
KillMode=process
TimeoutSec=30
Restart=no
StandardOutput=journal
StandardError=journal
SyslogIdentifier=patroni
NoNewPrivileges=true
ProtectHome=true
ReadWritePaths=$PG_DATA_DIR $PG_LOG_DIR $PG_ARCHIVE_DIR $PATRONI_LOG_DIR /tmp

[Install]
WantedBy=multi-user.target
EOF

    systemctl daemon-reload
    check_result "systemd服务文件创建"
}

# 创建管理脚本
create_management_scripts() {
    log_info "创建管理脚本..."

    # 创建集群状态检查脚本
    cat > "$PG_SCRIPTS_DIR/check_cluster.sh" << 'EOF'
#!/bin/bash
echo "=== PostgreSQL集群状态检查 ==="
echo "检查时间: $(date)"
echo

echo "1. Patroni集群状态:"
patronictl -c /home/<USER>/config/patroni.yml list

echo
echo "2. etcd集群状态:"
etcdctl --endpoints=http://**********:2379,http://**********:2379,http://***********:2379 endpoint health

echo
echo "3. PostgreSQL进程状态:"
ps aux | grep postgres | grep -v grep

echo
echo "4. 端口监听状态:"
ss -tuln | grep -E ":(5432|8008|2379|2380)"

echo "=== 检查完成 ==="
EOF

    # 创建备份脚本
    cat > "$PG_SCRIPTS_DIR/backup.sh" << EOF
#!/bin/bash
BACKUP_DIR="$PG_BACKUP_DIR"
DATE=\$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="postgres_backup_\${DATE}"

mkdir -p \${BACKUP_DIR}

echo "开始备份: \${BACKUP_NAME}"
pg_basebackup -h $NODE_IP -D \${BACKUP_DIR}/\${DATE} -U replicator -v -P -W -X stream

tar -czf \${BACKUP_DIR}/\${BACKUP_NAME}.tar.gz -C \${BACKUP_DIR} \${DATE}
rm -rf \${BACKUP_DIR}/\${DATE}

echo "备份完成: \${BACKUP_DIR}/\${BACKUP_NAME}.tar.gz"
EOF

    # 设置脚本权限
    chmod +x "$PG_SCRIPTS_DIR"/*.sh
    chown -R "$INSTALL_USER:$INSTALL_GROUP" "$PG_SCRIPTS_DIR"

    check_result "管理脚本创建"
}

# 主安装函数
main() {
    log_info "开始PostgreSQL 17.5集群节点安装 (Debian版)..."

    # 检测节点信息
    detect_node_info

    # 检查系统环境
    check_system

    # 安装依赖
    install_dependencies

    # 创建用户和目录
    create_user_and_dirs

    # 编译安装PostgreSQL
    install_postgresql

    # 安装etcd
    install_etcd

    # 安装Patroni
    install_patroni

    # 配置系统参数
    configure_system

    # 配置etcd
    configure_etcd

    # 初始化PostgreSQL
    init_postgresql

    # 配置Patroni
    configure_patroni

    # 创建systemd服务
    create_systemd_services

    # 创建管理脚本
    create_management_scripts

    log_info "PostgreSQL节点安装完成！"
    log_info "请在所有节点安装完成后，按以下顺序启动服务："
    log_info "1. 启动etcd: nohup etcd --config-file=/etc/etcd.conf > /home/<USER>/logs/etcd.log 2>&1 &"
    log_info "2. 启动Patroni: systemctl start patroni"
    log_info "3. 检查集群状态: $PG_SCRIPTS_DIR/check_cluster.sh"
}

# ============================================================================
# 脚本执行入口
# ============================================================================

# 检查参数
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "PostgreSQL 17.5集群节点安装脚本 (Debian版)"
    echo "用法: $0 [function_name]"
    echo "可用函数:"
    echo "  all                    - 执行完整安装 (默认)"
    echo "  detect_node_info       - 检测节点信息"
    echo "  check_system          - 检查系统环境"
    echo "  install_dependencies  - 安装系统依赖"
    echo "  create_user_and_dirs  - 创建用户和目录"
    echo "  install_postgresql    - 安装PostgreSQL"
    echo "  install_etcd          - 安装etcd"
    echo "  configure_python_mirror - 配置Python镜像源"
    echo "  install_patroni       - 安装Patroni"
    echo "  configure_system      - 配置系统参数"
    echo "  configure_etcd        - 配置etcd"
    echo "  init_postgresql       - 初始化PostgreSQL"
    echo "  configure_patroni     - 配置Patroni"
    echo "  create_systemd_services - 创建systemd服务"
    echo "  create_management_scripts - 创建管理脚本"
    exit 0
fi

# 根据参数执行指定函数
case "$1" in
    "detect_node_info")
        detect_node_info
        ;;
    "check_system")
        check_system
        ;;
    "install_dependencies")
        install_dependencies
        ;;
    "create_user_and_dirs")
        create_user_and_dirs
        ;;
    "install_postgresql")
        install_postgresql
        ;;
    "install_etcd")
        install_etcd
        ;;
    "configure_python_mirror")
        configure_python_mirror
        ;;
    "install_patroni")
        install_patroni
        ;;
    "configure_system")
        configure_system
        ;;
    "configure_etcd")
        configure_etcd
        ;;
    "init_postgresql")
        init_postgresql
        ;;
    "configure_patroni")
        configure_patroni
        ;;
    "create_systemd_services")
        create_systemd_services
        ;;
    "create_management_scripts")
        create_management_scripts
        ;;
    "all"|"")
        # 执行主函数
        main "$@"
        ;;
    *)
        echo "错误: 未知的函数名 '$1'"
        echo "使用 '$0 --help' 查看可用函数"
        exit 1
        ;;
esac